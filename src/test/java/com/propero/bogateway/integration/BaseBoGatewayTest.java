package com.propero.bogateway.integration;

import com.propero.bogateway.BoGatewayApplication;
import com.propero.bogateway.config.ApiProps;
import com.redis.testcontainers.RedisContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalManagementPort;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.web.client.RestTemplate;
import org.testcontainers.containers.BindMode;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.utility.DockerImageName;
import org.wiremock.integrations.testcontainers.WireMockContainer;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, classes = BoGatewayApplication.class)
@ActiveProfiles(value = "it")
public abstract class BaseBoGatewayTest {

    private static final Duration DEFAULT_CONTAINER_TIMEOUT = Duration.of(30, ChronoUnit.SECONDS);

    @Autowired
    protected ApiProps apiProps;
    @Autowired
    protected RetryTemplate retryTemplate;
    @Autowired
    protected RestTemplate restTemplate;

    @LocalManagementPort
    private Integer managementPort;

    @LocalServerPort
    private Integer serverPort;

    private static final WireMockContainer WIRE_MOCK_CONTAINER = new WireMockContainer(WireMockContainer.OFFICIAL_IMAGE_NAME)
            .withCliArg("--global-response-templating")
            .withClasspathResourceMapping("wiremock", "/home/<USER>/", BindMode.READ_ONLY)
            .withoutBanner();

    private static final RedisContainer REDIS_CONTAINER = new RedisContainer(DockerImageName.parse("redis:5.0.3-alpine"))
            .waitingFor(Wait.forSuccessfulCommand("redis-cli ping | grep PONG"))
            .withStartupTimeout(DEFAULT_CONTAINER_TIMEOUT)
            .withExposedPorts(6379);

    static {
        WIRE_MOCK_CONTAINER.start();
        REDIS_CONTAINER.start();
    }

    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("api.uris.hasuraServiceUri", WIRE_MOCK_CONTAINER::getBaseUrl);
        registry.add("api.uris.customerServiceUri", WIRE_MOCK_CONTAINER::getBaseUrl);
        registry.add("REDIS_HOST", REDIS_CONTAINER::getHost);
        registry.add("REDIS_PORT", () -> REDIS_CONTAINER.getMappedPort(6379).toString());
    }

    protected String getBaseUrl() {
        return "http://localhost:" + serverPort + "/" + apiProps.getApiPathPrefix();
    }

    protected String getManagementBaseUrl() {
        return "http://localhost:" + managementPort;
    }

}
