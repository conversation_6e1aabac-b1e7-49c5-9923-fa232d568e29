package com.propero.bogateway.integration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;

public class HealthCheckTest extends BaseBoGatewayTest {

    @Test
    public void readinessActuator() {
        ResponseEntity<AvailabilityResponse> liveness = restTemplate.getForEntity(getManagementBaseUrl() + "/actuator/health/liveness", AvailabilityResponse.class);
        assertThat(liveness.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(liveness.getBody()).extracting(AvailabilityResponse::getStatus).isEqualTo("UP");

        ResponseEntity<AvailabilityResponse> readiness = restTemplate.getForEntity(getManagementBaseUrl() + "/actuator/health/readiness", AvailabilityResponse.class);
        assertThat(readiness.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(liveness.getBody()).extracting(AvailabilityResponse::getStatus).isEqualTo("UP");
    }

    @Getter
    @RequiredArgsConstructor
    private static class AvailabilityResponse {
        private final String status;
    }
}
