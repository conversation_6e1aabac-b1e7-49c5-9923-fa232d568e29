package com.propero.bogateway.integration;


import com.c4_soft.springaddons.security.oauth2.test.annotations.WithOAuth2Login;
import com.propero.bogateway.config.AccessPermissions;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;

public class BoGatewayApplicationTest extends BaseBoGatewayTest {

    @Test
    public void getHealth() {
        ResponseEntity<Object> response = retryTemplate.execute(context -> restTemplate.getForEntity(getManagementBaseUrl() + "/actuator/health", Object.class));
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_CUST)
    public void authorizedAccessAllowed() {
        ResponseEntity<Object> response = retryTemplate.execute(context -> restTemplate.getForEntity(getBaseUrl() + "/customers", Object.class));
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
    }
}
