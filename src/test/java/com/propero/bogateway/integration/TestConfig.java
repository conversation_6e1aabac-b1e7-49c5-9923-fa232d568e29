package com.propero.bogateway.integration;

import io.netty.handler.logging.LogLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.gateway.config.HttpClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.RestTemplate;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

@Configuration
@Slf4j
class TestConfig {
    @Bean
    RetryTemplate testRetryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        retryTemplate.setRetryPolicy(new SimpleRetryPolicy(2));
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(2000L);
        retryTemplate.setBackOffPolicy(backOffPolicy);
        return retryTemplate;
    }

    @Bean
    public RestTemplate testRestTemplate(@Autowired RestTemplateBuilder builder) {
        return builder.build();
    }

    @Bean
    public HttpClientCustomizer httpClientCustomizer() {
        return httpClient -> httpClient.wiretap("access-logs", LogLevel.TRACE, AdvancedByteBufFormat.TEXTUAL);
    }
}
