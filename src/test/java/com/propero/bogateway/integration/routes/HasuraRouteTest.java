package com.propero.bogateway.integration.routes;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithOAuth2Login;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.integration.BaseBoGatewayTest;
import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class HasuraRouteTest extends BaseBoGatewayTest {

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficeTransactionsWithCustomerConditions() {
        Map<String, Object> variables = Map.of(
                "customerId", 13710L,
                "firstName", "John",
                "lastName", "Doe"
        );
        ResponseEntity<Map<String,Object>> response = fetchTransactions(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertTransactionsResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficeTransactionsWithTransactionConditions() {
        Map<String, Object> variables = Map.of("transactionId", "91112");
        ResponseEntity<Map<String,Object>> response = fetchTransactions(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertTransactionsResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficeTransactionsBetweenDates() {
        Map<String, Object> variables = Map.of(
                "fromTimestamp", "2023-05-20T14:00:00.000Z",
                "toTimestamp", "2023-05-21T01:00:59.999Z",
                "updatedFrom", "2023-03-20T14:00:00.000Z",
                "updatedTo", "2023-03-21T01:00:59.999Z"
        );
        ResponseEntity<Map<String,Object>> response = fetchTransactions(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertTransactionsResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficeTransactionsEmptyReport() {
        Map<String, Object> variables = Map.of("transactionId", "empty");
        ResponseEntity<Map<String,Object>> response = fetchTransactions(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        DocumentContext responseBody = JsonPath.parse(response.getBody());
        assertThat(responseBody.read("$.transactions", List.class)).isEmpty();
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficeFailedDepositsReportNoParameters() {
        Map<String, Object> variables = Map.of();
        ResponseEntity<Map<String,Object>> response = fetchFailedDeposits(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertFailedDepositResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficeFailedDepositsReportStartDate() {
        Map<String, Object> variables = Map.of(
                "fromTimestamp", "2023-05-20T14:00:00.000Z",
                "toTimestamp", "2023-05-21T01:00:59.999Z"
        );
        ResponseEntity<Map<String,Object>> response = fetchFailedDeposits(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertFailedDepositResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities = AccessPermissions.READ_TX)
    public void getBackOfficePendingWithdrawalsReport() {
        Map<String, Object> variables = Map.of("manual", true);
        ResponseEntity<Map<String,Object>> response = fetchPendingWithdrawals(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertPendingWithdrawalResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities =  AccessPermissions.READ_TX)
    public void getBackOfficeCustomersByEmail(){
        Map<String, Object> variables = Map.of(
            "email", "<EMAIL>",
            "customerCreatedAfter", "2023-02-01T00:00:00+01:00",
            "customerCreatedBefore", "2023-02-28T00:00+01:00",
            "countryOfResidence", "ES"
        );
        ResponseEntity<Map<String,Object>> response = fetchCustomers(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertCustomersResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities =  AccessPermissions.READ_TX)
    public void getBackOfficeCustomersByTradingAccountId(){
        Map<String, Object> variables = Map.of(
            "externalAccountId", "4412123",
            "realAccountCreatedAfter", "2023-02-01T00:00:00+01:00",
            "realAccountCreatedBefore", "2023-02-28T00:00+01:00"
        );
        ResponseEntity<Map<String,Object>> response = fetchCustomers(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertCustomersResponse(response.getBody());
    }

    @Test
    @WithOAuth2Login(authorities =  AccessPermissions.READ_TX)
    public void getBackOfficeCustomersByFirstDeposit(){
        Map<String, Object> variables = Map.of(
            "firstDepositAfter", "2023-02-01T00:00:00+01:00",
            "firstDepositBefore", "2023-02-28T00:00+01:00"
        );
        ResponseEntity<Map<String,Object>> response = fetchCustomers(variables);
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertCustomersResponse(response.getBody());
    }

    private ResponseEntity<Map<String,Object>> fetchTransactions(Map<String, Object> variables) {
        return retryTemplate.execute(context -> restTemplate.exchange(getBaseUrl() + "/transactions",
                HttpMethod.POST, new HttpEntity<>(variables),  new ParameterizedTypeReference<>() {}));
    }

    private ResponseEntity<Map<String,Object>> fetchCustomers(Map<String, Object> variables) {
        return retryTemplate.execute(context -> restTemplate.exchange(getBaseUrl() + "/v2/customers",
                HttpMethod.POST, new HttpEntity<>(variables),  new ParameterizedTypeReference<>() {}));
    }

    private ResponseEntity<Map<String,Object>> fetchFailedDeposits(Map<String, Object> variables) {
        return retryTemplate.execute(context -> restTemplate.exchange(getBaseUrl() + "/transactions/deposit/failed",
                HttpMethod.POST, new HttpEntity<>(variables),  new ParameterizedTypeReference<>() {}));
    }

    private ResponseEntity<Map<String,Object>> fetchPendingWithdrawals(Map<String, Object> variables) {
        return retryTemplate.execute(context -> restTemplate.exchange(getBaseUrl() + "/transactions/wd/requested",
                HttpMethod.POST, new HttpEntity<>(variables),  new ParameterizedTypeReference<>() {}));
    }

    private void assertTransactionsResponse(Map<String,Object> response) {
        DocumentContext responseBody = JsonPath.parse(response);
        assertThat(responseBody.read("$.transactions[0].customerId", Long.class)).isEqualTo(13710);
        assertThat(responseBody.read("$.transactions[0].email", String.class)).isEqualTo("<EMAIL>");
        assertThat(responseBody.read("$.transactions[0].firstName", String.class)).isEqualTo("Maria Antonia");
        assertThat(responseBody.read("$.transactions[0].providerTransactionType", String.class)).isEqualTo( "DEPOSIT");
        assertThat(responseBody.read("$.transactions[0].destinationType", String.class)).isEqualTo( "PRAXIS");
        assertThat(responseBody.read("$.transactions[0].amount", BigDecimal.class)).isEqualByComparingTo( new BigDecimal("201.3"));
    }

    private void assertCustomersResponse(Map<String,Object> response) {
        DocumentContext responseBody = JsonPath.parse(response);
        assertThat(responseBody.read("$.totalCount", Integer.class)).isEqualTo(1);
        assertThat(responseBody.read("$.customers", List.class)).hasSize(1);
        assertThat(responseBody.read("$.customers[0].tradingAccountType", String.class)).isEqualTo("REAL");
        assertThat(responseBody.read("$.customers[0].brand", String.class)).isEqualTo("SKILLING");
        assertThat(responseBody.read("$.customers[0].totalDeposit", BigDecimal.class)).isEqualTo("4089.79");
        assertThat(responseBody.read("$.customers[0].name", String.class)).isEqualTo("Frida Foobar");
    }

    private void assertFailedDepositResponse(Map<String,Object> response) {
        DocumentContext responseBody = JsonPath.parse(response);
        assertThat(responseBody.read("$.transactions[0].customerId", Long.class)).isEqualTo(240459);
        assertThat(responseBody.read("$.transactions[0].transactionId", Long.class)).isEqualTo(240459);
        assertThat(responseBody.read("$.transactions[0].reference", String.class)).isEqualTo("p2628776");
        assertThat(responseBody.read("$.transactions[0].name", String.class)).isEqualTo("asd d");
        assertThat(responseBody.read("$.transactions[0].paymentMethod", String.class)).isEqualTo( "Paypal");
        assertThat(responseBody.read("$.transactions[0].status", String.class)).isEqualTo( "FAILED");
        assertThat(responseBody.read("$.transactions[0].amount", BigDecimal.class)).isEqualByComparingTo( new BigDecimal("1187.84"));
    }

    private void assertPendingWithdrawalResponse(Map<String,Object> response) {
        DocumentContext responseBody = JsonPath.parse(response);
        assertThat(responseBody.read("$.transactions[0].customerId", Long.class)).isEqualTo(240459);
        assertThat(responseBody.read("$.transactions[0].transactionId", Long.class)).isEqualTo(240459);
        assertThat(responseBody.read("$.transactions[0].amount", BigDecimal.class)).isEqualByComparingTo( new BigDecimal("1187.84"));
        assertThat(responseBody.read("$.transactions[0].bankInfo.bankName", String.class)).isEqualTo( "Bank name 1");
        assertThat(responseBody.read("$.transactions[0].bankInfo.bankAccountAddress", String.class)).isEqualTo( "Bank account address 1");
        assertThat(responseBody.read("$.transactions[0].bankInfo.bankAccountName", String.class)).isEqualTo( "Bank account name 1");
        assertThat(responseBody.read("$.transactions[0].bankInfo.bankAccountNumber", String.class)).isEqualTo( "12345");
        assertThat(responseBody.read("$.transactions[0].bankInfo.iban", String.class)).isEqualTo( "SE35 5000 0000 0549 1000 0003");
        assertThat(responseBody.read("$.transactions[0].bankInfo.swiftCode", String.class)).isEqualTo( "sfgfgfgf");



    }
}
