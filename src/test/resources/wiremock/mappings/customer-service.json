{"mappings": [{"priority": 1, "request": {"urlPattern": "/customers", "method": "GET"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"permissions": ["customer.login", "customer.edit"]}, "transformers": ["response-template"]}}, {"priority": 1, "request": {"urlPattern": "/v1/sessions", "method": "POST", "bodyPatterns": [{"equalToJson": "{ \"email\": \"<EMAIL>\"  }", "ignoreArrayOrder": true, "ignoreExtraElements": true}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"customerId": "{{jsonPath request.body '$.password'}}", "userId": "{{jsonPath request.body '$.password'}}", "sessionId": "{{randomValue length=8 type='ALPHANUMERIC'}}", "permissions": ["customer.login", "customer.edit"]}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/v1/sessions", "method": "POST"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"customerId": "1{{randomValue length=3 type='NUMERIC'}}", "userId": "2{{randomValue length=3 type='NUMERIC'}}", "sessionId": "{{randomValue length=8 type='ALPHANUMERIC'}}", "permissions": ["customer.login", "customer.edit"]}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/customers/[0-9]+", "method": "GET"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"id": "{{request.requestLine.pathSegments.[1]}}", "email": "<EMAIL>"}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/v2/customers/[0-9]+", "method": "GET"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"id": "{{request.requestLine.pathSegments.[2]}}", "email": "<EMAIL>"}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/authentication/eid/register/[0-9]+", "method": "POST"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"id": "{{randomValue length=5 type='NUMERIC'}}", "roles": [{"permissions": [{"name": "test"}]}]}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/authentication/users/[0-9]+", "method": "GET"}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"roles": [{"permissions": [{"name": "test"}]}]}, "transformers": ["response-template"]}}, {"request": {"urlPattern": "/authentication/customers/password/forgot-password", "method": "POST"}, "response": {"status": 201}}, {"request": {"urlPattern": "/authentication/customers/password/reset-password", "method": "POST"}, "response": {"status": 201}}, {"request": {"urlPattern": "/authentication/customers/[0-9]+/change-password", "method": "PATCH"}, "response": {"status": 204}}]}