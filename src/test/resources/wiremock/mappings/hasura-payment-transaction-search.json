{"mappings": [{"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficePaymentTransactionSearch"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"transactionCondition": {"transaction_type": {"_eq": "DEPOSIT"}, "status": {"_eq": "FAILED"}}, "pageSize": 20, "pageOffset": 0, "sortBy": "desc"}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"results": {"stats": {"aggregate": {"count": 1}}, "transactions": [{"transactionId": 240459, "customerId": 240459, "reference": "p2628776", "currency": "NOK", "amount": 1187.84, "paymentMethod": "<PERSON><PERSON>", "status": "FAILED", "createdAt": "2025-08-05T14:58:38.541+00:00", "updatedAt": "2025-08-05T14:58:38.541+00:00", "customer": {"fullName": {"firstName": "asd", "lastName": "d"}}}]}}}, "transformers": ["response-template"]}}, {"priority": 1, "request": {"urlPattern": "/v1/graphql", "method": "POST", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.operationName", "contains": "BackOfficePaymentTransactionSearch"}}, {"matchesJsonPath": {"expression": "$.variables", "equalToJson": {"transactionCondition": {"created_at": {"_gte": "2023-05-20T14:00:00", "_lte": "2023-05-21T01:00:59.999"}, "transaction_type": {"_eq": "DEPOSIT"}, "status": {"_eq": "FAILED"}}, "pageSize": 20, "pageOffset": 0, "sortBy": "desc"}}}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json;charset=UTF-8"}, "jsonBody": {"data": {"results": {"stats": {"aggregate": {"count": 1}}, "transactions": [{"transactionId": 240459, "customerId": 240459, "reference": "p2628776", "currency": "NOK", "amount": 1187.84, "paymentMethod": "<PERSON><PERSON>", "status": "FAILED", "createdAt": "2025-08-05T14:58:38.541+00:00", "updatedAt": "2025-08-05T14:58:38.541+00:00", "customer": {"fullName": {"firstName": "asd", "lastName": "d"}}}]}}}, "transformers": ["response-template"]}}]}