spring:
  application:
    name: bo-gateway-test
  main:
    banner-mode: off
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: test-client
            client-secret: test-secret
            redirect-uri: http://localhost:3000/bog/login/oauth2/code/keycloak
            authorization-grant-type: authorization_code
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://localhost:8080/realms/test
            jwk-set-uri: http://localhost:8080/realms/test/protocol/openid-connect/certs
  cloud:
    gateway:
      httpclient:
        connect-timeout: 5000
  data:
    redis:
      host: localhost
      port: 6379
      # Use embedded Redis for tests if available, or disable Redis features
      timeout: 1000ms
      connect-timeout: 1000ms

server:
  port: 0  # Use random port for tests

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

bo-gateway:
  base-uri: http://localhost:3000
  proxyPathPrefix: /bog
  security:
    session:
      max-inactive-interval: PT60M
    connection-pool:
      max-life-time: PT1M

keycloak:
  realm: test
  base-uri: http://localhost:8080

api:
  uris:
    auditServiceUri: http://localhost:8081
    customerServiceUri: http://localhost:8082
    elasticsearchUri: http://localhost:9200
    feedServiceUri: http://localhost:8083
    kycServiceUri: http://localhost:8084
    paymentServiceUri: http://localhost:8085
    reportingServiceUri: http://localhost:8086
    tradingAccountServiceUri: http://localhost:8087
    tradingBalanceServiceUri: http://localhost:8088
    hasuraServiceUri: http://localhost:8089
  timeouts:
    extendedSearchTimeout: 60000

metrics:
  hostname: localhost
  environment-id: test
  allowed-class-tags:
  empty-class-tag-allowed: true

logging:
  level:
    com.propero: DEBUG
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.springframework.cloud.gateway: WARN

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
