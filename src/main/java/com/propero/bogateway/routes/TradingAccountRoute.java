package com.propero.bogateway.routes;

import com.propero.bogateway.config.AccessPermissions;
import com.propero.bogateway.config.ApiProps;
import com.propero.bogateway.helpers.RouteHelper;
import com.propero.bogateway.helpers.SecurityHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

@Configuration
@RequiredArgsConstructor
public class TradingAccountRoute {

    private final SecurityHelper securityHelper;
    private final RouteHelper routeHelper;
    private final ApiProps apiProps;

    @Bean
    public RouteLocator tradingAccountRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
                .route("get-customer-accounts", r -> r.path(routeHelper.prefixRoute("/tas/v1/customers/{id}/accounts"))
                        .and()
                        .method(HttpMethod.GET)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 1)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.READ_CUST)))
                        .uri(apiProps.getUris().getTradingAccountServiceUri()))

                .route("add-customer-account", r -> r.path(routeHelper.prefixRoute("/tas/v1/**"))
                        .and()
                        .method(HttpMethod.POST)
                        .filters(fSpec -> routeHelper.stripPrefix(fSpec, 1)
                                .filter(securityHelper.authIfHasAnyRole(AccessPermissions.WRITE_ACCOUNT)))
                        .uri(apiProps.getUris().getTradingAccountServiceUri()))
                .build();
    }
}
