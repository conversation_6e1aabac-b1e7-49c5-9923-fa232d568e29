package com.propero.bogateway.filters;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@RequiredArgsConstructor
@Configuration
@Slf4j
public class AddCustomHeadersGlobalFilter implements GlobalFilter {

    private static final String BO_USER_EMAIL_HEADER = "x-bo_user_email";
    private static final String KEY_CLOCK_USERNAME = "preferred_username";

    @Bean
    public GlobalFilter customFilter() {
        return new AddCustomHeadersGlobalFilter();
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        return exchange.getPrincipal()
                .filter(principal -> principal instanceof OAuth2AuthenticationToken)
                .switchIfEmpty(Mono.error(new InsufficientAuthenticationException("User not authenticated")))
                .map(principal -> {
                    OAuth2AuthenticationToken token = (OAuth2AuthenticationToken) principal;
                    OAuth2User user = token.getPrincipal();
                    String agentEmail = user.getAttribute(KEY_CLOCK_USERNAME);
                    exchange.getRequest().mutate().header(BO_USER_EMAIL_HEADER, agentEmail).build();
                    return exchange;
                })
                .flatMap(chain::filter);
    }

}
