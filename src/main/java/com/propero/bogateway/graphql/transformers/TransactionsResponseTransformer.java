package com.propero.bogateway.graphql.transformers;

import com.jayway.jsonpath.DocumentContext;
import com.propero.bogateway.graphql.GraphQLTransformer;
import com.propero.bogateway.helpers.GraphQLHelper;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.reactivestreams.Publisher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.util.Optional.ofNullable;

@RequiredArgsConstructor
public class TransactionsResponseTransformer implements
        GraphQLTransformer<Map<String,Object>, TransactionsResponseTransformer.TransactionResponse> {

    private final GraphQLHelper graphQLHelper;

    @Override
    public Publisher<TransactionResponse> transform(ServerWebExchange exchange, Map<String, Object> data) {
        DocumentContext bodyDoc = graphQLHelper.parse(data);
        Map<String, List<Map<String,Object>>> results = bodyDoc.read("$.data.results");
        List<TransactionResponse.Transaction> transactions = new ArrayList<>();
        if (results.containsKey("customers")) {
            transactions = flattenCustomers(results.get("customers"));
        } else if(results.containsKey("transactions")) {
            transactions = flattenTransactions(results.get("transactions"));
        }
        return Mono.just(TransactionResponse.builder().transactions(transactions).build());
    }

    private List<TransactionResponse.Transaction> flattenCustomers(List<Map<String,Object>> customers) {
        List<TransactionResponse.Transaction> transactions = new ArrayList<>();
        for (Map<String,Object> customer :customers) {
            DocumentContext customerDoc = graphQLHelper.parse(customer);
            List<Map<String,Object>> customerTransactions = customerDoc.read("$.transactions");
            transactions.addAll(flattenTransactions(customerTransactions));
        }
        return transactions;
    }

    private List<TransactionResponse.Transaction> flattenTransactions(List<Map<String,Object>> customerTransactions) {
        List<TransactionResponse.Transaction> transactions = new ArrayList<>();
        for(Map<String, Object> transaction: customerTransactions) {
            DocumentContext transactionDoc = graphQLHelper.parse(transaction);
            transactions.add(TransactionResponse.Transaction.builder()
                    .customerId(graphQLHelper.pathExists(transactionDoc,"$.customer") ?
                            transactionDoc.read("$.customer.customerId", Long.class) : null)
                    .email(graphQLHelper.pathExists(transactionDoc,"$.customer") ?
                            transactionDoc.read("$.customer.email") : null)
                    .firstName(graphQLHelper.pathExists(transactionDoc,"$.customer") ?
                            transactionDoc.read("$.customer.person.firstName") : null)
                    .lastName(graphQLHelper.pathExists(transactionDoc,"$.customer") ?
                            transactionDoc.read("$.customer.person.lastName") : null)
                    .transactionId(transactionDoc.read("$.transactionId"))
                    .internalTransactionId(transactionDoc.read("$.internalTransactionId"))
                    .createdAt(transactionDoc.read("$.createdAt"))
                    .updatedAt(transactionDoc.read("$.updatedAt"))
                    .tradingAccountId(transactionDoc.read("$.tradingAccountId"))
                    .delta(transactionDoc.read("$.delta", BigDecimal.class))
                    .transactionType(transactionDoc.read("$.transactionType"))
                    .license(transactionDoc.read("$.license"))
                    .provider(transactionDoc.read("$.provider"))
                    .providerTransactionType(transactionDoc.read("$.providerTransactionType"))
                    .paymentMethod(transactionDoc.read("$.paymentMethod"))
                    .operationType(transactionDoc.read("$.operationType"))
                    .currency(transactionDoc.read("$.currency"))
                    .description(transactionDoc.read("$.description"))
                    .externalOperationId(transactionDoc.read("$.externalOperationId"))
                    .status(transactionDoc.read("$.status"))
                    .description(transactionDoc.read("$.description"))
                    .destinationType(graphQLHelper.pathExists(transactionDoc, "$.payment") ?
                            transactionDoc.read("$.payment.destinationType") : null)
                    .paymentProcessor(graphQLHelper.pathExists(transactionDoc, "$.payment") ?
                            transactionDoc.read("$.payment.paymentProcessor") : null)
                    .comment(graphQLHelper.pathExists(transactionDoc, "$.payment") ?
                            transactionDoc.read("$.payment.comment") : null)
                    .amount(ofNullable(transactionDoc.read("$.amount", BigDecimal.class)).map(BigDecimal::abs).orElse(null))
                    .amountEur(ofNullable(transactionDoc.read("$.amountEur", BigDecimal.class)).map(BigDecimal::abs).orElse(null))
                    .build());
        }
        return transactions;
    }

    @Getter
    @Builder
    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static class TransactionResponse {
        private List<Transaction> transactions;

        @Getter
        @Builder
        @AllArgsConstructor(access = AccessLevel.PRIVATE)
        private static class Transaction {
            private Long customerId;
            private String email;
            private String firstName;
            private String lastName;
            private String transactionId;
            private String internalTransactionId;
            private String createdAt;
            private String updatedAt;
            private String tradingAccountId;
            private BigDecimal delta;
            private String transactionType;
            private String license;
            private String provider;
            private String providerTransactionType;
            private String paymentMethod;
            private String operationType;
            private String currency;
            private String description;
            private String externalOperationId;
            private String status;
            private String destinationType;
            private String paymentProcessor;
            private String comment;
            private BigDecimal amount;
            private BigDecimal amountEur;
        }
    }
}
