package com.propero.bogateway.config.security;

import com.propero.bogateway.config.GatewayProperties;
import com.propero.bogateway.config.PrefixAwareRequestCache;
import com.propero.bogateway.helpers.RouteHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity.CsrfSpec;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.oauth2.client.endpoint.OAuth2AuthorizationCodeGrantRequest;
import org.springframework.security.oauth2.client.endpoint.ReactiveOAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.WebClientReactiveAuthorizationCodeTokenResponseClient;
import org.springframework.security.oauth2.client.oidc.web.server.logout.OidcClientInitiatedServerLogoutSuccessHandler;
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository;
import org.springframework.security.oauth2.core.user.OAuth2UserAuthority;
import org.springframework.security.web.server.DelegatingServerAuthenticationEntryPoint;
import org.springframework.security.web.server.DelegatingServerAuthenticationEntryPoint.DelegateEntry;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.ServerAuthenticationEntryPoint;
import org.springframework.security.web.server.authentication.HttpStatusServerEntryPoint;
import org.springframework.security.web.server.authentication.RedirectServerAuthenticationEntryPoint;
import org.springframework.security.web.server.authentication.RedirectServerAuthenticationFailureHandler;
import org.springframework.security.web.server.authentication.logout.ServerLogoutSuccessHandler;
import org.springframework.session.config.ReactiveSessionRepositoryCustomizer;
import org.springframework.session.data.redis.ReactiveRedisSessionRepository;
import org.springframework.session.data.redis.config.annotation.web.server.EnableRedisWebSession;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.net.URI;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.springframework.http.HttpMethod.GET;
import static org.springframework.security.web.server.util.matcher.ServerWebExchangeMatchers.anyExchange;
import static org.springframework.security.web.server.util.matcher.ServerWebExchangeMatchers.pathMatchers;

@Configuration
@EnableWebFluxSecurity
@EnableRedisWebSession(redisNamespace = "bo-gw:spring:session")
@RequiredArgsConstructor
@Slf4j
public class SecurityConfig {

    private static final String PATH_PATTERN_TRIGGER_OIDC_FLOW = "/oauth2/authorization/keycloak";
    private static final String PATH_PATTERN_HANDLE_AUTH_CODE = "/login/oauth2/code/keycloak";
    private static final String PATH_PATTERN_LOGIN_ERROR = "/oauth2/error";
    private static final String PATH_PATTERN_LOGOUT = "/logout";

    private static final String PATH_PATTERN_ACTUATORS = "/actuator";
    private static final String PATH_PATTERN_ACTUATOR = "/actuator/**";

    private static final String PATH_PATTERN_SWAGGER_HTML = "/swagger-ui.html";
    private static final String PATH_PATTERN_WEBJARS = "/webjars/**";
    private static final String PATH_PATTERN_API_DOCS = "/v3/api-docs/**";

    private final GatewayProperties gatewayProperties;
    private final RouteHelper routeHelper;
    private final PrefixAwareRequestCache requestCache;
    private final ReactiveClientRegistrationRepository clientRegistrationRepository;

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {
        http
                .authorizeExchange(authorizeSpec -> authorizeSpec
                        .pathMatchers(GET, PATH_PATTERN_SWAGGER_HTML, PATH_PATTERN_WEBJARS, PATH_PATTERN_API_DOCS).permitAll()
                        .pathMatchers(GET, PATH_PATTERN_ACTUATORS, PATH_PATTERN_ACTUATOR).permitAll()
                        .pathMatchers(GET, PATH_PATTERN_TRIGGER_OIDC_FLOW, PATH_PATTERN_HANDLE_AUTH_CODE, PATH_PATTERN_ACTUATOR).permitAll()
                        .anyExchange().authenticated()
                )
                .oauth2Client(Customizer.withDefaults())
                .oauth2Login(loginSpec -> loginSpec
                        .authenticationFailureHandler(getAuthenticationFailureHandler())
                )
                .logout(logoutSpec -> logoutSpec
                        .requiresLogout(pathMatchers(GET, PATH_PATTERN_LOGOUT))
                        .logoutSuccessHandler(oidcLogoutSuccessHandler())
                )
                .exceptionHandling(exceptionSpec -> exceptionSpec
                        .authenticationEntryPoint(createAuthenticationEntryPoint())
                )
                .requestCache(requestCacheSpec -> requestCacheSpec
                        .requestCache(requestCache)
                )
                .csrf(CsrfSpec::disable);

        return http.build();
    }

    /**
     * Since the BO FE only makes XHR requests to BO Gateway, it is not appropriate for all
     * unauthorized requests to trigger the OIDC flow.
     * <p>
     * Only requests to the designated endpoint ({@code /oauth2/user-details?redirect_uri})
     * should trigger the auth flow, all other requests should just return a 401 so the FE
     * can handle the session refresh.
     */
    private ServerAuthenticationEntryPoint createAuthenticationEntryPoint() {
        DelegateEntry oidcRedirectEntryPoint = new DelegateEntry(
                pathMatchers(GET, OAuth2Routes.PATH_PATTERN_USER_DETAILS),
                new RedirectServerAuthenticationEntryPoint(routeHelper.applyProxyPrefix(PATH_PATTERN_TRIGGER_OIDC_FLOW))
        );

        DelegateEntry defaultEntryPoint = new DelegateEntry(
                anyExchange(),
                new HttpStatusServerEntryPoint(HttpStatus.UNAUTHORIZED)
        );

        return new DelegatingServerAuthenticationEntryPoint(oidcRedirectEntryPoint, defaultEntryPoint);
    }

    @Bean
    public ReactiveSessionRepositoryCustomizer<ReactiveRedisSessionRepository> redisSessionCustomizer() {
        return sessionRepository -> sessionRepository
                .setDefaultMaxInactiveInterval(gatewayProperties.getSecurity().getSession().getMaxInactiveInterval());
    }

    @Bean
    @SuppressWarnings("unchecked")
    public GrantedAuthoritiesMapper userAuthoritiesMapper() {
        return (authorities) -> {
            Set<GrantedAuthority> mappedAuthorities = new HashSet<>();

            authorities.forEach(authority -> {
                if (authority instanceof OAuth2UserAuthority oauth2UserAuthority) {
                    Map<String, Object> userAttributes = oauth2UserAuthority.getAttributes();

                    Map<String, ArrayList<String>> permissions = (Map<String, ArrayList<String>>) userAttributes.get("permissions");
                    ArrayList<String> roles = permissions.get("roles");
                    for (String role : roles) {
                        mappedAuthorities.add(new SimpleGrantedAuthority(role));
                    }
                }
            });

            return mappedAuthorities;
        };
    }

    @Bean
    public ReactiveOAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> reactiveOAuth2AccessTokenResponseClient() {
        WebClient webClient = WebClient.builder().clientConnector(new ReactorClientHttpConnector(HttpClient.create(connectionProvider()))).build();
        WebClientReactiveAuthorizationCodeTokenResponseClient accessTokenResponseClient = new WebClientReactiveAuthorizationCodeTokenResponseClient();
        accessTokenResponseClient.setWebClient(webClient);
        return accessTokenResponseClient;
    }

    private ConnectionProvider connectionProvider() {

        return ConnectionProvider.builder("oauth2-access-token-connection-pool")
                .maxLifeTime(gatewayProperties.getSecurity().getConnectionPool().getMaxLifeTime())
                .build();

    }

    private static RedirectServerAuthenticationFailureHandler getAuthenticationFailureHandler() {
        return new RedirectServerAuthenticationFailureHandler(PATH_PATTERN_LOGIN_ERROR);
    }

    private ServerLogoutSuccessHandler oidcLogoutSuccessHandler() {
        OidcClientInitiatedServerLogoutSuccessHandler logoutHandler = new OidcClientInitiatedServerLogoutSuccessHandler(clientRegistrationRepository);
        logoutHandler.setPostLogoutRedirectUri(gatewayProperties.getBaseUri());
        logoutHandler.setLogoutSuccessUrl(URI.create(gatewayProperties.getBaseUri()));
        return logoutHandler;
    }
}
