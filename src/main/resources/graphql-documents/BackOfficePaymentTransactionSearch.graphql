query BackOfficePaymentTransactionSearch(
    $transactionCondition: payment_transactions_bool_exp! = {}
    $pageSize:Int! = 25
    $pageOffset:Int! = 0
    $sortBy: order_by! = desc
){
    results: payment_db {

        stats: payment_transactions_aggregate( where: $transactionCondition){
            aggregate {
                count
            }
        }

        transactions: payment_transactions( where: $transactionCondition
            offset: $pageOffset
            limit: $pageSize
            order_by: {created_at: $sortBy}
        ) {
            transactionId: id
            customerId: customer_id
            reference
            currency
            amount
            paymentMethod: payment_method
            status
            createdAt: created_at
            updatedAt: updated_at
            bankInfo: manual_bank_info
            customer {
                fullName: person {
                    firstName: first_name
                    lastName: last_name
                }
            }
        }
    }
}
